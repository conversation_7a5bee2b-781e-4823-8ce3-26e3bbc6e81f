import segyio
import numpy as np
import matplotlib.pyplot as plt

def read_and_visualize_sgy(filename):
    """读取SGY文件并可视化"""
    try:
        # 打开SGY文件
        with segyio.open(filename, "r", ignore_geometry=True) as f:
            # 读取基本信息
            print(f"文件: {filename}")
            print(f"道数: {f.tracecount}")
            print(f"采样点数: {len(f.samples)}")
            print(f"采样间隔: {f.bin[segyio.BinField.Interval] / 1000} ms")
            print(f"采样率: {f.bin[segyio.BinField.Interval]} μs")
            
            # 读取所有道数据
            data = segyio.tools.collect(f.trace[:])
            
            # 获取时间轴
            samples = f.samples
            time_axis = samples / 1000.0  # 转换为秒
            
            return data, time_axis, f.tracecount
            
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None, None, None

def visualize_seismic_data(data, time_axis, trace_count):
    """可视化地震数据"""
    if data is None:
        return
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 8))
    
    # 图1: 显示所有道的波形图像
    im = ax1.imshow(data.T, cmap='seismic', aspect='auto', 
                   extent=[0, trace_count, time_axis[-1], time_axis[0]])
    ax1.set_xlabel('道号')
    ax1.set_ylabel('时间 (秒)')
    ax1.set_title('地震数据剖面')
    plt.colorbar(im, ax=ax1, label='振幅')
    
    # 图2: 显示前几道的波形
    num_traces_to_show = min(10, trace_count)
    for i in range(num_traces_to_show):
        ax2.plot(data[i] + i * 2, time_axis, linewidth=0.8, 
                label=f'道 {i+1}' if i < 5 else '')
    
    ax2.set_xlabel('振幅 (偏移)')
    ax2.set_ylabel('时间 (秒)')
    ax2.set_title(f'前 {num_traces_to_show} 道波形')
    ax2.invert_yaxis()
    ax2.grid(True, alpha=0.3)
    if num_traces_to_show <= 5:
        ax2.legend()
    
    plt.tight_layout()
    plt.show()

def main():
    """主函数"""
    sgy_file = "tornado.sgy"
    
    print("正在读取SGY文件...")
    data, time_axis, trace_count = read_and_visualize_sgy(sgy_file)
    
    if data is not None:
        print("\n开始可视化...")
        visualize_seismic_data(data, time_axis, trace_count)
        print("可视化完成！")
    else:
        print("无法读取文件，请检查文件是否存在或格式是否正确")

if __name__ == "__main__":
    main()
