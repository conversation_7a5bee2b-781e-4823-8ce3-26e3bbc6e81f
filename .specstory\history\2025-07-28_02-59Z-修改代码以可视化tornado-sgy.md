<!-- Generated by SpecStory -->

# 修改代码以可视化tornado.sgy (2025-07-28 02:59Z)

_**User**_

修改01-readsgy.py代码，读取tornado.sgy 并可视化

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求修改01-readsgy.py文件来读取tornado.sgy文件并可视化。首先我需要查看当前的01-readsgy.py文件内容，然后了解tornado.sgy文件的格式和内容。

从项目结构看，有两个文件：
- 01-readsgy.py（看起来是空的）
- tornado.sgy（这是一个SGY文件，通常是地震数据文件）

SGY文件是地震数据的标准格式（SEG-Y），通常用于地球物理数据存储。我需要：
1. 先读取现有的01-readsgy.py文件内容
2. 编写代码来读取tornado.sgy文件
3. 添加可视化功能

让我先看看现有的代码文件内容。</details></think>

---

我需要先查看当前的01-readsgy.py文件内容，然后编写代码来读取tornado.sgy文件并进行可视化。

---

Read file: 01-readsgy.py

---

<think><details><summary>Thought Process</summary>文件是空的，我需要从零开始编写代码。SGY文件是地震数据格式，通常需要使用专门的库来读取，比如segyio、ObsPy等。我需要编写代码来：

1. 读取tornado.sgy文件
2. 解析数据
3. 进行可视化

让我编写一个完整的代码来实现这个功能。</details></think>

---

文件是空的，我将编写完整的代码来读取tornado.sgy文件并进行可视化。SGY文件是地震数据格式，我会使用segyio库来读取数据。

---

<details><summary>Edit file: 01-readsgy.py</summary>

编写完整的代码来读取tornado.sgy文件并可视化地震数据

**Chunk 1**
Lines added: 79, lines removed: 1

```diff
@@ -1,1 +1,79 @@
- 
+ import segyio
+ import numpy as np
+ import matplotlib.pyplot as plt
+ 
+ def read_and_visualize_sgy(filename):
+     """读取SGY文件并可视化"""
+     try:
+         # 打开SGY文件
+         with segyio.open(filename, "r", ignore_geometry=True) as f:
+             # 读取基本信息
+             print(f"文件: {filename}")
+             print(f"道数: {f.tracecount}")
+             print(f"采样点数: {len(f.samples)}")
+             print(f"采样间隔: {f.bin[segyio.BinField.Interval] / 1000} ms")
+             print(f"采样率: {f.bin[segyio.BinField.Interval]} μs")
+             
+             # 读取所有道数据
+             data = segyio.tools.collect(f.trace[:])
+             
+             # 获取时间轴
+             samples = f.samples
+             time_axis = samples / 1000.0  # 转换为秒
+             
+             return data, time_axis, f.tracecount
+             
+     except Exception as e:
+         print(f"读取文件时出错: {e}")
+         return None, None, None
+ 
+ def visualize_seismic_data(data, time_axis, trace_count):
+     """可视化地震数据"""
+     if data is None:
+         return
+     
+     # 创建图形
+     fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 8))
+     
+     # 图1: 显示所有道的波形图像
+     im = ax1.imshow(data.T, cmap='seismic', aspect='auto', 
+                    extent=[0, trace_count, time_axis[-1], time_axis[0]])
+     ax1.set_xlabel('道号')
+     ax1.set_ylabel('时间 (秒)')
+     ax1.set_title('地震数据剖面')
+     plt.colorbar(im, ax=ax1, label='振幅')
+     
+     # 图2: 显示前几道的波形
+     num_traces_to_show = min(10, trace_count)
+     for i in range(num_traces_to_show):
+         ax2.plot(data[i] + i * 2, time_axis, linewidth=0.8, 
+                 label=f'道 {i+1}' if i < 5 else '')
+     
+     ax2.set_xlabel('振幅 (偏移)')
+     ax2.set_ylabel('时间 (秒)')
+     ax2.set_title(f'前 {num_traces_to_show} 道波形')
+     ax2.invert_yaxis()
+     ax2.grid(True, alpha=0.3)
+     if num_traces_to_show <= 5:
+         ax2.legend()
+     
+     plt.tight_layout()
+     plt.show()
+ 
+ def main():
+     """主函数"""
+     sgy_file = "tornado.sgy"
+     
+     print("正在读取SGY文件...")
+     data, time_axis, trace_count = read_and_visualize_sgy(sgy_file)
+     
+     if data is not None:
+         print("\n开始可视化...")
+         visualize_seismic_data(data, time_axis, trace_count)
+         print("可视化完成！")
+     else:
+         print("无法读取文件，请检查文件是否存在或格式是否正确")
+ 
+ if __name__ == "__main__":
+     main()
+ 
```

</details>

---

<think><details><summary>Thought Process</summary>代码已经写好了。现在用户可以运行这个脚本来读取tornado.sgy文件并可视化。但是用户需要先安装必要的依赖包。我应该告诉用户需要安装的包，然后可以运行代码。

不过根据用户规则，我不应该自动创建requirements.txt文件，也不应该提供额外的使用示例。我只需要告诉用户代码已经准备好了，如果需要的话可以提及需要安装的依赖。</details></think>

---

代码已经修改完成。这个程序能够读取tornado.sgy文件并创建两种可视化：
1. 地震数据剖面图像
2. 前几道的波形图

运行前需要安装依赖：`pip install segyio matplotlib numpy`

程序会显示文件的基本信息（道数、采样点数、采样间隔等），然后生成可视化图表。

---

_**User**_



---

_**Assistant**_



---

