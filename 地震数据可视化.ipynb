# -*- coding: utf-8 -*-

import matplotlib.pyplot as plt
import segyio
import numpy as np

file_2D = 'Model94_shots.segy'
file_3D = 'Kerry3D.segy'

''' 2D seismic data read       '''
##读取数据
f = segyio.open(file_2D, ignore_geometry=True)
f.mmap()#mmap将一个文件或者其它对象映射进内存，加快读取速度
data2D = np.asarray([np.copy(x) for x in f.trace[:]]).T#(2000,123600)

''' 2D seismic data vasual       '''
###直接对原始数据可视化
clip = 1e+4#显示范围，负值越大越明显
vmin, vmax = -clip, clip

fig = plt.figure(figsize=(5,5))
plt.subplot(1, 1, 1)
imgplot1 = plt.imshow(data2D[250:550,0:220],cmap=plt.cm.seismic,interpolation='nearest',aspect=0.5,vmin=vmin, vmax=vmax)
#imgplot1 = plt.imshow(data2D[250:550,0:220],cmap=plt.cm.bone,interpolation='nearest',aspect=0.5,vmin=vmin, vmax=vmax)
#imgplot1 = plt.imshow(data2D[250:550,0:220],cmap='Greys',interpolation='nearest',aspect=0.5,vmin=vmin, vmax=vmax)

''' 3D seismic data process   '''

def chunks(s, n):
    """Produce `n`-character chunks（大块） from string `s`."""
    for start in range(0, len(s), n):
        yield s[start:start + n]

with segyio.open(file_3D, strict=False) as s:
    
    # Read the data.
    data3D = np.stack(t.astype(np.float) for t in s.trace)
    
    # Get the (x, y) locations.
    x = [t[segyio.TraceField.GroupX] for t in s.header]
    y = [t[segyio.TraceField.GroupY] for t in s.header]
    
    # Get the trace numbers.
    cdp = np.array([t[segyio.TraceField.CDP] for t in s.header])

    # Get the first textual header.
    header = s.text[0].decode('ascii')
    formatted = '\n'.join(chunk for chunk in chunks(header, 80))

    # Get data from the binary header.
    # Get the sample interval in ms (convert from microsec).
    sample_interval = s.bin[segyio.BinField.Interval] / 1000

print(formatted)        



inline_range = abs(510-796) + 1#287
xl_range = abs(58-792) + 1#735
inline_range * xl_range == cdp.shape[0]#True，说明算出的INLINES和XLINES结果正确
cube = data3D.reshape((inline_range, xl_range, -1))#(287, 735, 1252)
data = cube[100,10:512,10:500].T

'''画出一个2D剖面图'''
clip = 1e+1#显示范围，负值越大越明显
vmin, vmax = -clip, clip
fig = plt.figure(figsize=(10,10))
plt.subplot(1, 1, 1)
imgplot1 = plt.imshow(data,cmap=plt.cm.seismic,interpolation='nearest',aspect=0.5,vmin=vmin, vmax=vmax)

#####################################
'''展示3D的数据  3张剖面 '''
#####################################
from mayavi import mlab
import scipy.ndimage
##数据归一化
cube1 = cube[:,:300,:300]
cube1 = cube1-np.min(cube1)
cube1 = cube1/np.max(cube1)
cube1 = cube1*255

source = mlab.pipeline.scalar_field(cube1)#scalar_field获得数据的标量数据场
source.spacing = [1, 1, -1]

for axis in ['x','y', 'z']:#x,y,z代表了3个剖面
    plane = mlab.pipeline.image_plane_widget(source, 
                                    plane_orientation='{}_axes'.format(axis),##设置切平面的方向
                                    slice_index=100, colormap='gray')
    # Flip colormap. Better way to do this?
    plane.module_manager.scalar_lut_manager.reverse_lut = True
mlab.show()

#####################################
'''展示3D的数据  z轴为立体剖面'''
#####################################

z= cube1[100,:,:]
# Clip out some spikes and smooth the surface a bit...
z = scipy.ndimage.median_filter(z, 4)
z = scipy.ndimage.gaussian_filter(z, 1)

# Quirks due to the way hor.grid is defined...
mlab.surf(np.arange(z.shape[0]), np.arange(z.shape[1]), -z)

source = mlab.pipeline.scalar_field(cube1)
source.spacing = [1, 1, -1]

for axis in ['x', 'y']:
    plane = mlab.pipeline.image_plane_widget(source, 
                                    plane_orientation='{}_axes'.format(axis),
                                    slice_index=100, colormap='gray')
    plane.module_manager.scalar_lut_manager.reverse_lut = True
mlab.show()

